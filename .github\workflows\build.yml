name: Build NuGet Packages

on:
  push:
    branches:
      - main

jobs:
  check-nuget:
    runs-on: ubuntu-latest
    outputs:
      skip: ${{ steps.check.outputs.all_exist }}
      version: ${{ steps.get-version.outputs.version }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Read version from file
        id: get-version
        run: |
          VERSION=$(cat VERSION)
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Check if all NuGet packages already exist
        id: check
        run: |
          VERSION="${{ steps.get-version.outputs.version }}"
          PACKAGE_IDS=(tickerq tickerq.dashboard tickerq.utilities tickerq.entityframeworkcore)
          MISSING=0

          for ID in "${PACKAGE_IDS[@]}"; do
            LOWER_ID=$(echo "$ID" | tr '[:upper:]' '[:lower:]')
            URL="https://api.nuget.org/v3-flatcontainer/$LOWER_ID/$VERSION/$LOWER_ID.$VERSION.nupkg"
            STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$URL")

            if [ "$STATUS" = "200" ]; then
              echo "✅ $ID $VERSION exists on NuGet"
            else
              echo "❌ $ID $VERSION does NOT exist on NuGet"
              MISSING=1
            fi
          done

          if [ "$MISSING" = "0" ]; then
            echo "all_exist=true" >> $GITHUB_OUTPUT
          else
            echo "all_exist=false" >> $GITHUB_OUTPUT
          fi

  build:
    needs: check-nuget
    if: needs.check-nuget.outputs.skip != 'true'
    runs-on: ubuntu-latest
    env:
      DOTNET_NOLOGO: true

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup .NET SDKs
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: |
            3.0.x
            5.0.x
            6.0.x
            7.0.x
            8.0.x
            9.0.x

      - name: Setup Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install frontend dependencies
        run: npm install
        working-directory: src/TickerQ.Dashboard/wwwroot

      - name: Build frontend
        run: npm run build
        working-directory: src/TickerQ.Dashboard/wwwroot

      - name: Build TickerQ.Utilities
        run: dotnet build src/TickerQ.Utilities/TickerQ.Utilities.csproj --configuration Release /p:PackageVersion=${{ needs.check-nuget.outputs.version }}

      - name: Pack TickerQ.Utilities
        run: dotnet pack src/TickerQ.Utilities/TickerQ.Utilities.csproj --configuration Release --output ./nupkgs /p:PackageVersion=${{ needs.check-nuget.outputs.version }}

      - name: Add local nupkgs source
        run: dotnet nuget add source "$(pwd)/nupkgs" --name LocalNupkgs

      - name: Build TickerQ.SourceGenerator
        run: dotnet build src/TickerQ.SourceGenerator/TickerQ.SourceGenerator.csproj --configuration Release

      - name: Build other projects
        run: |
          dotnet build src/TickerQ/TickerQ.csproj --configuration Release /p:PackageVersion=${{ needs.check-nuget.outputs.version }}
          dotnet build src/TickerQ.EntityFrameworkCore/TickerQ.EntityFrameworkCore.csproj --configuration Release /p:PackageVersion=${{ needs.check-nuget.outputs.version }}
          dotnet build src/TickerQ.Dashboard/TickerQ.Dashboard.csproj --configuration Release /p:PackageVersion=${{ needs.check-nuget.outputs.version }}

      - name: Pack other projects
        run: |
          dotnet pack src/TickerQ/TickerQ.csproj --configuration Release --output ./nupkgs /p:PackageVersion=${{ needs.check-nuget.outputs.version }}
          dotnet pack src/TickerQ.EntityFrameworkCore/TickerQ.EntityFrameworkCore.csproj --configuration Release --output ./nupkgs /p:PackageVersion=${{ needs.check-nuget.outputs.version }}
          dotnet pack src/TickerQ.Dashboard/TickerQ.Dashboard.csproj --configuration Release --output ./nupkgs /p:PackageVersion=${{ needs.check-nuget.outputs.version }}

      - name: Show .nupkg file sizes
        run: |
          echo "📦 Package sizes:"
          for pkg in ./nupkgs/*.nupkg; do
            size=$(du -h "$pkg" | cut -f1)
            echo " - $(basename "$pkg"): $size"
          done

      - name: Upload nupkgs artifact
        uses: actions/upload-artifact@v4
        with:
          name: nupkgs
          path: ./nupkgs

  trigger-publish:
    needs:
      - check-nuget
      - build
    runs-on: ubuntu-latest

    steps:
      - name: Get artifact ID for nupkgs
        id: get_artifact
        run: |
          artifacts=$(gh api repos/${{ github.repository }}/actions/runs/${{ github.run_id }}/artifacts)
          id=$(echo "$artifacts" | jq '.artifacts[] | select(.name=="nupkgs") | .id')
          echo "ARTIFACT_ID=$id" >> $GITHUB_ENV
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Trigger publish.yml with artifact ID
        uses: actions/github-script@v7
        env:
          GH_PAT: ${{ secrets.PAT_TOKEN }}
        with:
          script: |
            const artifactId = process.env.ARTIFACT_ID;
            const version = '${{ needs.check-nuget.outputs.version }}';

            const res = await fetch('https://api.github.com/repos/${{ github.repository }}/actions/workflows/publish.yml/dispatches', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${process.env.GH_PAT}`,
                'Accept': 'application/vnd.github.v3+json'
              },
              body: JSON.stringify({
                ref: 'main',
                inputs: {
                  version: version,
                  artifact_id: artifactId
                }
              })
            });

            if (!res.ok) {
              const body = await res.text();
              core.setFailed(`❌ Failed to dispatch: ${res.status} ${res.statusText}\n${body}`);
            } else {
              console.log(`✅ Successfully triggered publish with artifact ID: ${artifactId}`);
            }
