name: Publish NuGet

on:
  workflow_dispatch:
    inputs:
      version:
        required: true
        type: string
      artifact_id:
        required: true
        type: string

jobs:
  download:
    runs-on: ubuntu-latest
    environment:
      name: production-publish

    steps:
      - name: Download artifact ZIP by ID
        run: |
          curl -L -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/${{ github.repository }}/actions/artifacts/${{ github.event.inputs.artifact_id }}/zip \
            -o artifact.zip

      - name: Extract artifact
        run: unzip artifact.zip -d ./nupkgs

      - name: Publish to NuGet
        run: |
          for f in ./nupkgs/*.nupkg; do
            dotnet nuget push "$f" --source https://api.nuget.org/v3/index.json --api-key ${{ secrets.NUGET_API_KEY }}
          done
