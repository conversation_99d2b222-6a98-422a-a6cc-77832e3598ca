﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TickerQ.Utilities", "src\TickerQ.Utilities\TickerQ.Utilities.csproj", "{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TickerQ.EntityFrameworkCore", "src\TickerQ.EntityFrameworkCore\TickerQ.EntityFrameworkCore.csproj", "{A96850C7-35AD-4F5B-B31C-67333D55C548}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TickerQ.Dashboard", "src\TickerQ.Dashboard\TickerQ.Dashboard.csproj", "{67C99E39-EF62-4A83-B613-93AE39FC9DFD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TickerQ", "src\TickerQ\TickerQ.csproj", "{C69BE610-27A6-4720-849D-5A7AEF56BADE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TickerQ.SourceGenerator", "src\TickerQ.SourceGenerator\TickerQ.SourceGenerator.csproj", "{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{BA086F2D-2778-4F58-A9AA-45F560CE3504}"
	ProjectSection(SolutionItems) = preProject
		src\Directory.Build.props = src\Directory.Build.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".Solution Items", ".Solution Items", "{9A4EB4A4-FB92-477C-A6D8-0735579B7BAB}"
	ProjectSection(SolutionItems) = preProject
		Directory.Build.props = Directory.Build.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{61397D4B-87E7-4BE8-A674-EBCEDFBAE441}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TickerQ.Tests", "tests\TickerQ.Tests\TickerQ.Tests.csproj", "{4FA316D2-A206-45F2-AC16-69420E83E211}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "samples", "samples", "{45D577FA-DB7A-4B96-BB3F-97DDA0A929D5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TickerQ.HelloWorld", "samples\HelloWorld\TickerQ.HelloWorld.csproj", "{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TickerQ.WithDependecyInjection", "samples\WithDependencyInjection\TickerQ.WithDependencyInjection.csproj", "{FC775FFF-3856-434A-9E5F-D8D6477981BA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Debug|x64.ActiveCfg = Debug|Any CPU
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Debug|x64.Build.0 = Debug|Any CPU
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Debug|x86.ActiveCfg = Debug|Any CPU
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Debug|x86.Build.0 = Debug|Any CPU
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Release|Any CPU.Build.0 = Release|Any CPU
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Release|x64.ActiveCfg = Release|Any CPU
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Release|x64.Build.0 = Release|Any CPU
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Release|x86.ActiveCfg = Release|Any CPU
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24}.Release|x86.Build.0 = Release|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Debug|x64.Build.0 = Debug|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Debug|x86.Build.0 = Debug|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Release|Any CPU.Build.0 = Release|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Release|x64.ActiveCfg = Release|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Release|x64.Build.0 = Release|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Release|x86.ActiveCfg = Release|Any CPU
		{A96850C7-35AD-4F5B-B31C-67333D55C548}.Release|x86.Build.0 = Release|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Debug|x64.Build.0 = Debug|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Debug|x86.ActiveCfg = Debug|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Debug|x86.Build.0 = Debug|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Release|Any CPU.Build.0 = Release|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Release|x64.ActiveCfg = Release|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Release|x64.Build.0 = Release|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Release|x86.ActiveCfg = Release|Any CPU
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD}.Release|x86.Build.0 = Release|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Debug|x64.Build.0 = Debug|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Debug|x86.Build.0 = Debug|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Release|Any CPU.Build.0 = Release|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Release|x64.ActiveCfg = Release|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Release|x64.Build.0 = Release|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Release|x86.ActiveCfg = Release|Any CPU
		{C69BE610-27A6-4720-849D-5A7AEF56BADE}.Release|x86.Build.0 = Release|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Debug|x64.Build.0 = Debug|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Debug|x86.Build.0 = Debug|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Release|Any CPU.Build.0 = Release|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Release|x64.ActiveCfg = Release|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Release|x64.Build.0 = Release|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Release|x86.ActiveCfg = Release|Any CPU
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9}.Release|x86.Build.0 = Release|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Debug|x64.ActiveCfg = Debug|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Debug|x64.Build.0 = Debug|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Debug|x86.ActiveCfg = Debug|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Debug|x86.Build.0 = Debug|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Release|Any CPU.Build.0 = Release|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Release|x64.ActiveCfg = Release|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Release|x64.Build.0 = Release|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Release|x86.ActiveCfg = Release|Any CPU
		{4FA316D2-A206-45F2-AC16-69420E83E211}.Release|x86.Build.0 = Release|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Debug|x64.Build.0 = Debug|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Debug|x86.Build.0 = Debug|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Release|Any CPU.Build.0 = Release|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Release|x64.ActiveCfg = Release|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Release|x64.Build.0 = Release|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Release|x86.ActiveCfg = Release|Any CPU
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B}.Release|x86.Build.0 = Release|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Debug|x64.Build.0 = Debug|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Debug|x86.Build.0 = Debug|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Release|x64.ActiveCfg = Release|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Release|x64.Build.0 = Release|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Release|x86.ActiveCfg = Release|Any CPU
		{FC775FFF-3856-434A-9E5F-D8D6477981BA}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C69BE610-27A6-4720-849D-5A7AEF56BADE} = {BA086F2D-2778-4F58-A9AA-45F560CE3504}
		{67C99E39-EF62-4A83-B613-93AE39FC9DFD} = {BA086F2D-2778-4F58-A9AA-45F560CE3504}
		{A96850C7-35AD-4F5B-B31C-67333D55C548} = {BA086F2D-2778-4F58-A9AA-45F560CE3504}
		{1AE368A3-E3D9-4ECF-8979-5EAF8AFAADA9} = {BA086F2D-2778-4F58-A9AA-45F560CE3504}
		{68DBCCE9-C774-4215-8CC1-7284D0D4DB24} = {BA086F2D-2778-4F58-A9AA-45F560CE3504}
		{4FA316D2-A206-45F2-AC16-69420E83E211} = {61397D4B-87E7-4BE8-A674-EBCEDFBAE441}
		{EBC1EA7E-19A6-4B71-B84B-5A188BE7448B} = {45D577FA-DB7A-4B96-BB3F-97DDA0A929D5}
		{FC775FFF-3856-434A-9E5F-D8D6477981BA} = {45D577FA-DB7A-4B96-BB3F-97DDA0A929D5}
	EndGlobalSection
EndGlobal
