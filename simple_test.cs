using System;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using TickerQ.Utilities;
using TickerQ.Utilities.Base;
using TickerQ.Utilities.Enums;
using TickerQ.Utilities.Models;

namespace SimpleTest
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing RegisterFunctionsFromAssemblies...");
            Console.WriteLine("==========================================");

            // Test 1: Register functions from current assembly
            Console.WriteLine("\n1. Testing registration from current assembly...");
            TestRegisterFromCurrentAssembly();

            // Test 2: Test with null and empty assemblies
            Console.WriteLine("\n2. Testing with null and empty assemblies...");
            TestNullAndEmptyAssemblies();

            Console.WriteLine("\nAll tests completed!");
        }

        static void TestRegisterFromCurrentAssembly()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                TickerFunctionProvider.RegisterFunctionsFromAssemblies(new[] { assembly });

                var functions = TickerFunctionProvider.TickerFunctions;
                Console.WriteLine($"Registered functions count: {functions?.Count ?? 0}");

                if (functions != null)
                {
                    foreach (var func in functions)
                    {
                        Console.WriteLine($"  - Function: {func.Key}, Cron: {func.Value.Item1}, Priority: {func.Value.Item2}");
                    }
                }

                Console.WriteLine("✓ Current assembly registration test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Current assembly registration test failed: {ex.Message}");
            }
        }

        static void TestNullAndEmptyAssemblies()
        {
            try
            {
                // Test with null
                TickerFunctionProvider.RegisterFunctionsFromAssemblies(null);
                Console.WriteLine("✓ Null assemblies test passed");

                // Test with empty array
                TickerFunctionProvider.RegisterFunctionsFromAssemblies(new Assembly[0]);
                Console.WriteLine("✓ Empty assemblies test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Null/Empty assemblies test failed: {ex.Message}");
            }
        }
    }

    // Test classes with TickerFunction attributes
    public class TestTickerService
    {
        [TickerFunction("TestFunction1", "0 */5 * * * *", TickerTaskPriority.Normal)]
        public async Task TestMethod1(CancellationToken cancellationToken, TickerFunctionContext context)
        {
            Console.WriteLine("  TestFunction1 executed!");
            await Task.Delay(100, cancellationToken);
        }

        [TickerFunction("TestFunction2", TickerTaskPriority.High)]
        public void TestMethod2(TickerFunctionContext context)
        {
            Console.WriteLine("  TestFunction2 executed!");
        }

        [TickerFunction("TestFunction3")]
        public static async Task TestStaticMethod(CancellationToken cancellationToken)
        {
            Console.WriteLine("  TestFunction3 (static) executed!");
            await Task.Delay(50, cancellationToken);
        }
    }
} 