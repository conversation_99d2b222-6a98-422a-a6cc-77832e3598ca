﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>netstandard2.1;netcoreapp3.0;net5.0;net6.0;net7.0;net8.0;net9.0</TargetFrameworks>
        <PackageId>TickerQ.Dashboard</PackageId>
        <Description>Dashboard UI for visualizing and monitoring TickerQ scheduled jobs, status, and system metrics.</Description>
        <PackageTags>$(PackageTags);dashboard;monitoring;scheduler;status;job;ui</PackageTags>
        <PackageReadmeFile>README.md</PackageReadmeFile>
    </PropertyGroup>

    <ItemGroup>
        <None Include="README.md" Pack="true" PackagePath="" />
    </ItemGroup>

    <ItemGroup Condition="'$(TargetFramework)' == 'netstandard2.1' OR '$(TargetFramework)' == 'netcoreapp2.2' OR '$(TargetFramework)' == 'netcoreapp3.0'">
        <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.0.4" />
        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
        <PackageReference Include="Microsoft.AspNetCore.SpaServices" Version="2.2.7" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="2.2.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.2.0" />
        <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.2.0" />
        <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="2.2.0" />
        <PackageReference Include="Microsoft.Extensions.FileProviders.Physical" Version="2.2.0" />
        <PackageReference Include="System.Text.Json" Version="4.6.0" />
    </ItemGroup>

    <ItemGroup Condition="'$(TargetFramework)' != 'netstandard2.1' AND '$(TargetFramework)' != 'netcoreapp2.2' AND '$(TargetFramework)' != 'netcoreapp3.0'">
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="[1.0.0,)" />
        <PackageReference Include="Microsoft.AspNetCore.SpaServices" Version="[2.2.7,)" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="[3.1.0,)" />
        <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="[2.2.0,)" />
        <PackageReference Include="Microsoft.AspNetCore.Cors" Version="[2.2.0,)" />
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\TickerQ.Utilities\TickerQ.Utilities.csproj" />
    </ItemGroup>

    <PropertyGroup Condition="'$(TargetFramework)' != 'netstandard2.1' AND '$(TargetFramework)' != 'netcoreapp2.2' AND '$(TargetFramework)' != 'netcoreapp3.0'">
        <DefineConstants>$(DefineConstants);NETCOREAPP3_1_OR_GREATER</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <EmbeddedResource Include="wwwroot\dist\**\*" />
    </ItemGroup>

    <ItemGroup>
        <None Remove="wwwroot\src\**\*" />
        <None Remove="wwwroot\node_modules\**\*" />
        <None Remove="wwwroot\**\.vite\**\*" />
        <None Remove="wwwroot\**\*.map" />
        <None Remove="wwwroot\*" />
        <None Remove="wwwroot\public\*" />
    </ItemGroup>

</Project>