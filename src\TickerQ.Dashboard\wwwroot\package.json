{"name": "vue-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@microsoft/signalr": "^8.0.7", "axios": "^1.7.9", "echarts": "^5.6.0", "pinia": "^2.3.0", "timeago.js": "^4.0.2", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0", "vue-the-mask": "^0.11.1", "vuetify": "^3.7.6", "yup": "^1.6.1"}, "devDependencies": {"@mdi/font": "^7.4.47", "@tsconfig/node22": "^22.0.0", "@types/axios": "^0.9.36", "@types/node": "^22.10.2", "@types/vue-the-mask": "^0.11.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "npm-run-all2": "^7.0.2", "postcss": "^8.5.1", "prettier": "^3.3.3", "tailwindcss": "^3.4.17", "typescript": "~5.6.3", "vite": "^6.0.5", "vite-plugin-dts": "^4.5.0", "vite-plugin-vue-devtools": "^7.6.8", "vue-tsc": "^2.2.0"}}