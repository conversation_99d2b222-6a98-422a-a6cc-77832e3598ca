﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>netstandard2.1</TargetFramework>
		<PackageId>TickerQ.EntityFrameworkCore</PackageId>
		<Description>Entity Framework Core integration for scheduling and persisting cron/time-based jobs using TickerQ.</Description>
		<PackageTags>$(PackageTags);efcore;entityframework</PackageTags>
		<PackageReadmeFile>README.md</PackageReadmeFile>
	</PropertyGroup>

	<ItemGroup>
		<None Include="README.md" Pack="true" PackagePath="" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="[2.2.0,)" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="[2.2.0,)" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="[2.2.0,)" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\TickerQ.Utilities\TickerQ.Utilities.csproj" />
	</ItemGroup>
</Project>