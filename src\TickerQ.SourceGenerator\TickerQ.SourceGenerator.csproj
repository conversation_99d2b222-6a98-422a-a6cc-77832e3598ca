<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>netstandard2.0;</TargetFrameworks>
        <AssemblyName>TickerQ.SourceGenerator</AssemblyName>
        <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
        <EnforceExtendedAnalyzerRules>true</EnforceExtendedAnalyzerRules>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="[4.10.0,)" PrivateAssets="all" />
        <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="[3.3.4,)" PrivateAssets="all" />
    </ItemGroup>

</Project>
