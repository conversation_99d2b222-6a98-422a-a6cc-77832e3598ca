﻿
<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>netstandard2.1</TargetFramework>
		<PackageId>TickerQ.Utilities</PackageId>
		<Description>Simple utilities for queuing and executing cron/time-based jobs in the background.</Description>
		<PackageReadmeFile>README.md</PackageReadmeFile>
	</PropertyGroup>

	<ItemGroup>
		<None Include="README.md" Pack="true" PackagePath="" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="[2.1.0,)" />
		<PackageReference Include="NCrontab.Signed" Version="[3.3.2,)" />
		<PackageReference Include="System.Text.Json" Version="4.6.0" />
	</ItemGroup>

</Project>