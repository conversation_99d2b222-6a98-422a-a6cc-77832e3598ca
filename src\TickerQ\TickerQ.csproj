﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netstandard2.1</TargetFramework>
        <Description>A lightweight, developer-friendly library for queuing and executing cron and time-based jobs in the background.</Description>
        <PackageTags>$(PackageTags);background-jobs;task-scheduling</PackageTags>
        <PackageId>TickerQ</PackageId>
        <PackageReadmeFile>README.md</PackageReadmeFile>
    </PropertyGroup>

    <ItemGroup>
        <None Include="..\..\README.md" Pack="true" PackagePath="" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="[2.2.0,)"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="2.2.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="2.2.0"/>
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="2.2.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\TickerQ.Utilities\TickerQ.Utilities.csproj" />
    </ItemGroup>

    <Target Name="CopyAnalyzer" AfterTargets="Build">
        <MakeDir Directories="$(OutputPath)analyzers/dotnet/cs" />
    </Target>

    <ItemGroup>
        <None Include="..\TickerQ.SourceGenerator\bin\$(Configuration)\netstandard2.0\TickerQ.SourceGenerator.dll"
              Pack="true"
              PackagePath="analyzers/dotnet/cs" />
    </ItemGroup>

</Project>