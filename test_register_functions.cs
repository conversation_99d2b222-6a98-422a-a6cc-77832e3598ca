using System;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using TickerQ.Utilities;
using TickerQ.Utilities.Base;
using TickerQ.Utilities.Enums;
using TickerQ.Utilities.Models;

namespace TestRegisterFunctions
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing RegisterFunctionsFromAssemblies...");
            Console.WriteLine("==========================================");

            // Test 1: Register functions from current assembly
            Console.WriteLine("\n1. Testing registration from current assembly...");
            TestRegisterFromCurrentAssembly();

            // Test 2: Register functions from multiple assemblies
            Console.WriteLine("\n2. Testing registration from multiple assemblies...");
            TestRegisterFromMultipleAssemblies();

            // Test 3: Test with null and empty assemblies
            Console.WriteLine("\n3. Testing with null and empty assemblies...");
            TestNullAndEmptyAssemblies();

            // Test 4: Test function discovery and execution
            Console.WriteLine("\n4. Testing function discovery and execution...");
            await TestFunctionDiscoveryAndExecution();

            Console.WriteLine("\nAll tests completed!");
        }

        static void TestRegisterFromCurrentAssembly()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                TickerFunctionProvider.RegisterFunctionsFromAssemblies(new[] { assembly });

                var functions = TickerFunctionProvider.TickerFunctions;
                Console.WriteLine($"Registered functions count: {functions?.Count ?? 0}");

                if (functions != null)
                {
                    foreach (var func in functions)
                    {
                        Console.WriteLine($"  - Function: {func.Key}, Cron: {func.Value.Item1}, Priority: {func.Value.Item2}");
                    }
                }

                Console.WriteLine("✓ Current assembly registration test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Current assembly registration test failed: {ex.Message}");
            }
        }

        static void TestRegisterFromMultipleAssemblies()
        {
            try
            {
                var assemblies = new[]
                {
                    Assembly.GetExecutingAssembly(),
                    typeof(TickerFunctionProvider).Assembly,
                    typeof(TickerOptionsBuilder).Assembly
                };

                TickerFunctionProvider.RegisterFunctionsFromAssemblies(assemblies);

                var functions = TickerFunctionProvider.TickerFunctions;
                Console.WriteLine($"Registered functions count: {functions?.Count ?? 0}");

                Console.WriteLine("✓ Multiple assemblies registration test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Multiple assemblies registration test failed: {ex.Message}");
            }
        }

        static void TestNullAndEmptyAssemblies()
        {
            try
            {
                // Test with null
                TickerFunctionProvider.RegisterFunctionsFromAssemblies(null);
                Console.WriteLine("✓ Null assemblies test passed");

                // Test with empty array
                TickerFunctionProvider.RegisterFunctionsFromAssemblies(new Assembly[0]);
                Console.WriteLine("✓ Empty assemblies test passed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Null/Empty assemblies test failed: {ex.Message}");
            }
        }

        static async Task TestFunctionDiscoveryAndExecution()
        {
            try
            {
                // Register functions from current assembly
                var assembly = Assembly.GetExecutingAssembly();
                TickerFunctionProvider.RegisterFunctionsFromAssemblies(new[] { assembly });

                var functions = TickerFunctionProvider.TickerFunctions;
                if (functions != null && functions.Count > 0)
                {
                    Console.WriteLine($"Found {functions.Count} functions:");
                    foreach (var func in functions)
                    {
                        Console.WriteLine($"  - {func.Key}");
                    }

                    // Test execution of a function
                    var testFunction = functions["TestFunction1"];
                    if (testFunction.Delegate != null)
                    {
                        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
                        var mockServiceProvider = new MockServiceProvider();
                        var context = new TickerFunctionContext();

                        Console.WriteLine("Executing TestFunction1...");
                        await testFunction.Delegate(cts.Token, mockServiceProvider, context);
                        Console.WriteLine("✓ Function execution test passed");
                    }
                }
                else
                {
                    Console.WriteLine("No functions found in assembly");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Function discovery and execution test failed: {ex.Message}");
            }
        }
    }

    // Test classes with TickerFunction attributes
    public class TestTickerService
    {
        [TickerFunction("TestFunction1", "0 */5 * * * *", TickerTaskPriority.Normal)]
        public async Task TestMethod1(CancellationToken cancellationToken, TickerFunctionContext context)
        {
            Console.WriteLine("  TestFunction1 executed!");
            await Task.Delay(100, cancellationToken);
        }

        [TickerFunction("TestFunction2", TickerTaskPriority.High)]
        public void TestMethod2(TickerFunctionContext context)
        {
            Console.WriteLine("  TestFunction2 executed!");
        }

        [TickerFunction("TestFunction3")]
        public static async Task TestStaticMethod(CancellationToken cancellationToken)
        {
            Console.WriteLine("  TestFunction3 (static) executed!");
            await Task.Delay(50, cancellationToken);
        }
    }

    public class TestTickerServiceWithRequest
    {
        [TickerFunction("TestFunctionWithRequest", "0 0 * * * *")]
        public async Task TestMethodWithRequest(TickerFunctionContext<TestRequest> context, CancellationToken cancellationToken)
        {
            Console.WriteLine("  TestFunctionWithRequest executed!");
            await Task.Delay(100, cancellationToken);
        }
    }

    public class TestRequest
    {
        public string Message { get; set; }
        public int Value { get; set; }
    }

    // Mock service provider for testing
    public class MockServiceProvider : IServiceProvider
    {
        public object GetService(Type serviceType)
        {
            if (serviceType == typeof(TestTickerService))
                return new TestTickerService();
            if (serviceType == typeof(TestTickerServiceWithRequest))
                return new TestTickerServiceWithRequest();
            return null;
        }
    }
} 