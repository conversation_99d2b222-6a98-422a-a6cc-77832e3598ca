﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.2"/>
        <PackageReference Include="FluentAssertions" Version="8.4.0" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1"/>
        <PackageReference Include="NSubstitute" Version="5.3.0" />
        <PackageReference Include="xunit" Version="2.9.2"/>
        <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2"/>
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\TickerQ.Utilities\TickerQ.Utilities.csproj" />
    </ItemGroup>

</Project>
